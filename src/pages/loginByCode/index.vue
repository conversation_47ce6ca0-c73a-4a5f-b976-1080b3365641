<template>
  <view class="container">
    <CustomNavbar title="" background-color="#f8f8f8" />
    <view class="content">
      <view class="loginName">{{ isCodeLogin ? '验证码登录' : '密码登录' }}</view>
      <view class="input_contain">
        <!-- 手机号 -->
        <view class="input_cell">
          <input v-model="phoneNumber" placeholder="请输入手机号" placeholder-class="place-holder" maxlength="11" />
        </view>
        <!-- 密码 -->
        <view class="input_cell" v-show="!isCodeLogin">
          <input v-model="password" placeholder="请输入密码" type="password" placeholder-class="place-holder" />
        </view>
        <!-- 验证码 -->
        <view class="input_cell" v-show="isCodeLogin">
          <input v-model="verifyCode" placeholder="请输入验证码" placeholder-class="place-holder" />
          <text class="checkCode" @click.stop="getCode" v-if="countdown <= 0 && isCodeLogin"> 获取验证码 </text>
          <text class="checkCode" v-if="countdown > 0 && isCodeLogin">{{ countdown }}s</text>
        </view>
      </view>
      <!-- 按钮模块 -->
      <button class="login_btn" @click="handleLogin">登录</button>
      <!-- 切换登录方式 -->
      <view class="register" @click="switchLoginType">
        <text v-if="!isCodeLogin">验证码登录</text>
        <text v-else>密码登录</text>
      </view>
      <view class="assignment" @click="checkSelect">
        <image v-if="isSelect" class="icon" src="/static/icon/selected.png"></image>
        <image v-else class="icon" src="/static/icon/no-select.png"></image>
        <view v-if="showTip" class="tip">请勾选同意后进行登录</view>
        <text>我已阅读并同意<text class="primary">《用户协议》</text>和<text class="primary">《隐私政策》</text></text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, nextTick, onUnmounted, ref } from 'vue'

import CustomNavbar from '@/components/navbar/index.vue'

import { getVerifyCode, loginByCode } from '@/apis/user'

// 响应式数据
const isCodeLogin = ref(true) // 是否为验证码登录模式
const phoneNumber = ref('') // 手机号
const password = ref('') // 密码
const verifyCode = ref('') // 验证码
const countdown = ref(0) // 倒计时
const isSelect = ref(false) // 是否同意协议
const showTip = ref(false) // 是否显示提示

let countdownTimer: any = null

// 计算属性
const isPhoneValid = computed(() => {
  return /^1[3-9]\d{9}$/.test(phoneNumber.value)
})

const canLogin = computed(() => {
  if (!isSelect.value) return false
  if (!isPhoneValid.value) return false

  if (isCodeLogin.value) {
    return verifyCode.value.length
  } else {
    return password.value.length
  }
})

// 获取验证码
const getCode = async () => {
  if (!isPhoneValid.value) {
    uni.showToast({
      title: '请输入正确的手机号',
      icon: 'none',
    })
    return
  }

  if (!isSelect.value) {
    showTip.value = true
    setTimeout(() => {
      showTip.value = false
    }, 2000)
    return
  }

  await getVerifyCode({
    phone: phoneNumber.value,
  })

  uni.showToast({
    title: '验证码已发送',
    icon: 'success',
  })

  // 开始倒计时
  startCountdown()
}

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60
  countdownTimer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(countdownTimer)
      countdownTimer = null
    }
  }, 1000)
}

// 验证码登录
const handleLogin = async () => {
  if (!canLogin.value) {
    if (!isSelect.value) {
      showTip.value = true
      setTimeout(() => {
        showTip.value = false
      }, 2000)
      return
    }

    uni.showToast({
      title: '请填写完整信息',
      icon: 'none',
    })
    return
  }

  const params = isCodeLogin.value
    ? {
        phone: phoneNumber.value,
        code: verifyCode.value,
      }
    : {
        phone: phoneNumber.value,
        password: password.value,
      }

  await loginByCode(params)

  uni.showToast({
    title: '登录成功',
    icon: 'success',
  })
}

// 切换登录方式
const switchLoginType = async () => {
  try {
    // 清除倒计时
    if (countdownTimer) {
      clearInterval(countdownTimer)
      countdownTimer = null
      countdown.value = 0
    }

    // 先清空输入值
    password.value = ''
    verifyCode.value = ''

    // 使用 nextTick 确保状态清理完成后再切换模式
    await nextTick()

    // 切换模式
    isCodeLogin.value = !isCodeLogin.value

    console.log('切换登录方式:', isCodeLogin.value ? '验证码登录' : '密码登录')
  } catch (error) {
    console.error('切换登录方式失败:', error)
  }
}

// 切换协议选择状态
const checkSelect = () => {
  isSelect.value = !isSelect.value
  if (showTip.value) {
    showTip.value = false
  }
}

// 页面卸载时清理定时器
onUnmounted(() => {
  countdownTimer && clearInterval(countdownTimer)
})
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100vh;
  background: #f8f8f8;

  .content {
    width: 100%;
    box-sizing: border-box;
    padding: 0 46rpx;
  }
}

.loginName {
  font-size: 46rpx;
  color: #000000;
  padding-top: 80rpx;
  font-weight: bold;
  font-family:
    Source Han Sans CN,
    Source Han Sans CN;
}

.input_contain {
  margin-top: 80rpx;

  .input_cell {
    width: 100%;
    height: 100rpx;
    padding: 0 20rpx;
    box-sizing: border-box;
    border-bottom: 1rpx solid #dbdbdb;
    position: relative;

    input {
      width: 70%;
      height: 100%;
      line-height: 100rpx;
      color: #000000;
      font-size: 28rpx;
    }

    image {
      width: 26rpx;
      height: 36rpx;
    }
  }
}

.place-holder {
  color: #999999;
  font-size: 28rpx;
  font-weight: normal;
}

.login_btn {
  margin-top: 120rpx;
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 16rpx;
  background: #00a8cf;
  color: #fff;
  border: none;
  font-size: 28rpx;
  font-weight: bold;

  &::after {
    content: none;
    border: none;
  }
}

.register {
  color: #00a8cf;
  text-align: center;
  padding-top: 55rpx;
  font-size: 28rpx;
  cursor: pointer;
}

.checkCode {
  position: absolute;
  right: 20rpx;
  bottom: 0;
  font-size: 30rpx;
  color: #00a8cf;
  z-index: 999;
  line-height: 100rpx;
  display: block;
}

.assignment {
  margin-top: 50rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;

  .icon {
    width: 24rpx;
    height: 24rpx;
    margin-right: 10rpx;
  }

  text {
    font-size: 22rpx;
    color: #999999;

    .primary {
      color: #00a8cf;
    }
  }

  .tip {
    background: rgba(0, 0, 0, 0.5);
    font-size: 22rpx;
    color: #f8f8f8;
    padding: 20rpx 34rpx;
    border-radius: 8rpx;
    position: absolute;
    left: 50rpx;
    bottom: -92rpx;

    &::before {
      content: '';
      position: absolute;
      width: 0;
      height: 0;
      border-left: 10rpx solid transparent;
      border-right: 10rpx solid transparent;
      border-bottom: 10rpx solid rgba(0, 0, 0, 0.5);
      bottom: 100%;
      left: 44rpx;
    }
  }
}
</style>
