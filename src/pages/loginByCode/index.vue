<template>
  <view class="container" style="background: url({{imgUrl}}/login-bg.png) no-repeat; background-size: cover">
    <view class="navbar" style="padding-top: {{navTop}}px; height: {{navHeight}}px;">
      <image src="/images/icon/back.png" bindtap="goBack"></image>
      <text>登录</text>
      <view></view>
    </view>
    <view class="content">
      <view class="logo">
        <image class="logo-img" src="{{imgUrl}}/login-logo.jpg"></image>
      </view>
      <button wx:if="{{isSelect}}" class="phone-btn" open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber">
        手机号一键登录
      </button>
      <button wx:else class="phone-btn" bindtap="checkPhone">手机号一键登录</button>
      <button class="code-btn" bindtap="loginBySmsMethod">验证码登录</button>
    </view>
    <view class="assignment" bindtap="checkSelect">
      <image wx:if="{{isSelect}}" class="icon" src="/images/icon/selected.png"></image>
      <image wx:else class="icon" src="/images/icon/no-select.png"></image>
      <view wx:if="{{showTip}}" class="tip">请勾选同意后再进行登录</view>
      <text>我已阅读并同意<text class="primary">《用户协议》</text>和<text class="primary">《隐私政策》</text></text>
    </view>
    <!-- <zero-privacy
      :onNeed="agreePrivacyMode"
      @needAuthorization="handleNeedAuthorization"
      @agree="handleNeedAuthorization(false)"
      @disagree="handleDisAgree"
      title="用户隐私保护提示"
      predesc="在使用本程序前，请仔细阅读"
      privacy-contract-name-custom="《用户隐私保护指引》"
      subdesc="如您同意以上协议，点击同意开始使用。"
      position="bottom"
    ></zero-privacy> -->
  </view>
</template>

<script setup lang="ts"></script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100vh;
  position: relative;
}
.container .navbar {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-family:
    Source Han Sans CN,
    Source Han Sans CN;
  font-weight: bold;
  font-size: 34rpx;
  color: #000000;
}
.container .navbar view,
.container .navbar image {
  width: 44rpx;
  height: 44rpx;
  padding: 0 20rpx;
}
.container .content {
  width: 100%;
  padding: 200rpx 96rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
}
.container .logo {
  width: 180rpx;
  height: 180rpx;
  border-radius: 34rpx;
  background: #fefefe;
  box-shadow: 0rpx 3rpx 6rpx 1rpx rgba(0, 168, 207, 0.24);
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 6rpx;
  box-sizing: border-box;
}
.container .logo .logo-img {
  width: 150rpx;
  height: 150rpx;
}
.container .phone-btn {
  width: 100%;
  height: 96rpx;
  line-height: 96rpx;
  background: #00a8cf;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #f8f8f8;
  margin-top: 110rpx;
}
.container .code-btn {
  width: 100%;
  height: 96rpx;
  line-height: 96rpx;
  background: #ffffff;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #00a8cf;
  margin-top: 30rpx;
  border: 1px solid #00a8cf;
}
.assignment {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  position: absolute;
  bottom: calc(env(safe-area-inset-bottom) + 38rpx);
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
}

.assignment .icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 10rpx;
}

.assignment text {
  font-size: 22rpx;
  color: #999999;
}

.assignment text .primary {
  color: #00a8cf;
}

.assignment .tip {
  background: rgba(0, 0, 0, 0.5);
  font-size: 22rpx;
  color: #f8f8f8;
  padding: 20rpx 34rpx;
  border-radius: 8rpx;
  position: absolute;
  left: 62rpx;
  top: 58rpx;
}

.assignment .tip::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-left: 10rpx solid transparent;
  border-right: 10rpx solid transparent;
  border-bottom: 16rpx solid rgba(0, 0, 0, 0.5);
  top: -16rpx;
  left: 46rpx;
}
</style>
