<template>
  <view class="container">
    <view class="navbar" style="padding-top: {{navTop}}px; height: {{navHeight}}px;">
      <image src="/images/icon/back.png" bindtap="goBack"></image>
    </view>
    <view class="content">
      <view class="loginName">{{switch ? '验证码登录' : '密码登录'}}</view>
      <view class="input_contain">
        <!-- 手机号 -->
        <view class="input_cell">
          <input value="" placeholder="请输入手机号" bindinput="phoneNum" placeholder-class="place-holder" />
        </view>
        <!-- 身份证 -->
        <view class="input_cell" wx:if="{{!switch}}">
          <input
            value=""
            placeholder="请输入密码"
            type="password"
            bindinput="cardId"
            placeholder-class="place-holder"
          />
        </view>
        <!-- 验证码 -->
        <view class="input_cell" wx:if="{{switch}}" style="position: relative">
          <input value="" placeholder="验证码" bindinput="code" placeholder-class="place-holder" />
          <text class="checkCode" catchtap="getCode" wx:if="{{time<=0 && switch}}"> 获取验证码 </text>
          <text class="checkCode" wx:if="{{time>0 && switch}}">{{ time }}s</text>
        </view>
      </view>
      <!-- 按钮模块 -->
      <button class="login_btn" bindtap="login" wx:if="{{!switch}}">登录</button>
      <button class="login_btn" catchtap="isfocus" wx:if="{{switch}}" data-value="{{yanzhengma}}">登录</button>
      <!-- 注册模块 -->
      <view class="register" bindtap="register">
        <text wx:if="{{!switch}}">验证码登录</text>
        <text wx:if="{{switch}}">密码登录</text>
      </view>
      <view class="assignment" bindtap="checkSelect">
        <image wx:if="{{isSelect}}" class="icon" src="/images/icon/selected.png"></image>
        <image wx:else class="icon" src="/images/icon/no-select.png"></image>
        <view wx:if="{{showTip}}" class="tip">请勾选同意后进行登录</view>
        <text>我已阅读并同意<text class="primary">《用户协议》</text>和<text class="primary">《隐私政策》</text></text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts"></script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100vh;
  background: #f8f8f8;
}

.container .navbar {
  width: 100%;
  display: flex;
  align-items: center;
}

.container .navbar image {
  width: 44rpx;
  height: 44rpx;
  padding: 0 20rpx;
}

.container .content {
  width: 100%;
  box-sizing: border-box;
  padding: 0 46rpx;
}

.loginName {
  font-size: 46rpx;
  color: #000000;
  padding-top: 80rpx;
  font-weight: bold;
  font-family:
    Source Han Sans CN,
    Source Han Sans CN;
}

.input_contain {
  margin-top: 80rpx;
}

.input_contain .input_cell {
  width: 100%;
  height: 100rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  border-bottom: 1rpx solid #dbdbdb;
}

.input_contain input {
  width: 70%;
  height: 100%;
  line-height: 100rpx;
  color: #000000;
  font-size: 28rpx;
}
.place-holder {
  color: #999999;
  font-size: 28rpx;
  font-weight: normal;
}

.input_cell image {
  width: 26rpx;
  height: 36rpx;
}
.login_btn {
  margin-top: 120rpx;
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 16rpx;
  background: #00a8cf;
  color: #fff;
  border: none;
  font-size: 28rpx;
  font-weight: bold;
}

button::after {
  content: none;
  border: none;
}

.register {
  color: #00a8cf;
  text-align: center;
  padding-top: 55rpx;
  font-size: 28rpx;
}

.checkCode {
  position: absolute;
  right: 20rpx;
  bottom: 0;
  font-size: 30rpx;
  color: #00a8cf;
  z-index: 999;
  line-height: 100rpx;
  display: block;
}
.iptbox {
  width: 140rpx;
  display: block;
  margin: 0 auto;
  border: 1rpx solid #ddd;
  border-radius: 15rpx;
}

.assignment {
  margin-top: 50rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.assignment .icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 10rpx;
}

.assignment text {
  font-size: 22rpx;
  color: #999999;
}

.assignment text .primary {
  color: #00a8cf;
}

.assignment .tip {
  background: rgba(0, 0, 0, 0.5);
  font-size: 22rpx;
  color: #f8f8f8;
  padding: 20rpx 34rpx;
  border-radius: 8rpx;
  position: absolute;
  left: 62rpx;
  top: 58rpx;
}

.assignment .tip::before {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-left: 10rpx solid transparent;
  border-right: 10rpx solid transparent;
  border-bottom: 16rpx solid rgba(0, 0, 0, 0.5);
  top: -16rpx;
  left: 46rpx;
}
</style>
