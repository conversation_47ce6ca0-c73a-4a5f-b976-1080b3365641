<template>
  <z-paging
    ref="paging"
    v-model="dataList"
    refresher-enabled
    bg-color="#ffffff"
    :paging-style="{ height: 'calc(100vh - 50px - env(safe-area-inset-bottom))' }"
    :fixed="false"
    @refresh="onRefresh"
    @query="queryList"
  >
    <template #top>
      <view class="find-page-top">
        <CustomNavbar title="找房" :show-back="false" />
        <view class="find-page-top-box">
          <view class="search-header">
            <image src="@/static/icon/search-icon.png" class="search-icon" />
            <input
              type="text"
              placeholder="请输入小区名称"
              v-model="searchKeyword"
              @confirm="handleSearch"
              class="search-input"
              confirm-type="search"
            />
            <view class="search-btn" @click="handleSearch">搜索</view>
          </view>
          <view class="setion">
            <view class="setion-item" v-for="item in setionList" :key="item.value" @click="chooseSetion(item)">
              <text :class="{ active: item.chooseValue }">{{ item.label }}</text>
              <image v-if="item.isCurrent" class="icon" src="@/static/icon/choose-open.png"></image>
              <image v-else class="icon" src="@/static/icon/choose-close.png"></image>
            </view>
            <image class="change-icon" src="@/static/icon/choose-change.png"></image>
          </view>
        </view>
        <view class="line"></view>
      </view>
    </template>
    <view class="list">
      <view class="list-item" v-for="(room, index) in dataList" :key="index">
        <RoomCard :room-data="room" />
      </view>
    </view>
    <template slot="empty">
      <view class="empty"> 空 </view>
    </template>
  </z-paging>
  <ChoosePopup ref="choosePopupRef" :top="topHeight" :setionList="setionList" />
  <Tabbar />
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, onMounted, ref } from 'vue'

import CustomNavbar from '@/components/navbar/index.vue'
import RoomCard from '@/components/room-card/index.vue'
import Tabbar from '@/components/tabbar/index.vue'

import { getElementRect, sleep } from '@/utils'

import ChoosePopup from './components/ChoosePopup.vue'

const instance = getCurrentInstance()
const topHeight = ref(0)
const choosePopupRef = ref()
const paging = ref()
const dataList = ref()
const searchKeyword = ref('')
const setionList = ref([
  {
    label: '类型',
    value: 'type',
    list: [
      {
        label: '不限',
        value: 'buxian',
      },
      {
        label: '距离从近到远',
        value: 'jin',
      },
      {
        label: '距离从远到近',
        value: 'yuan',
      },
    ],
    isCurrent: false,
    chooseValue: '',
  },
  {
    label: '位置',
    value: 'location',
    list: [
      {
        label: '不限',
        value: 'buxian',
      },
      {
        label: '距离从近到远',
        value: 'jin',
      },
      {
        label: '距离从远到近',
        value: 'yuan',
      },
    ],
    isCurrent: false,
    chooseValue: '',
  },
  {
    label: '房型',
    value: 'roomType',
    list: [
      {
        label: '不限',
        value: 'buxian',
      },
      {
        label: '租金从低到高',
        value: 'di',
      },
      {
        label: '租金从高到低',
        value: 'gao',
      },
    ],
    isCurrent: false,
    chooseValue: '',
  },
  {
    label: '租金',
    value: 'price',
    list: [
      {
        label: '不限',
        value: 'buxian',
      },
      {
        label: '租金从低到高',
        value: 'di',
      },
      {
        label: '租金从高到低',
        value: 'gao',
      },
    ],
    isCurrent: false,
    chooseValue: '',
  },
])

const getNavTopHeight = async () => {
  await sleep()
  const res = await getElementRect('.find-page-top', instance)
  topHeight.value = res.height
}

const chooseSetion = (item) => {
  if (item.isCurrent) {
    item.isCurrent = false
    choosePopupRef.value.close()
    return
  }

  setionList.value.forEach((v) => {
    v.isCurrent = v.value === item.value
  })
  choosePopupRef.value.open()
}

const handleSearch = () => {}

const onRefresh = () => {}

const queryList = () => {
  setTimeout(() => {
    paging.value.complete([
      {
        id: 1,
        title: '整租·精品一居·菊园社区',
        area: '28.9',
        floor: 9,
        totalFloor: 11,
        orientationName: '南',
        address: '上海市嘉定区祁连路1255号',
        price: '1550',
        image: 'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
        tag: '保租房',
      },
      {
        id: 2,
        title: '整租·精品一居·菊园社区',
        area: '28.9',
        floor: 9,
        totalFloor: 11,
        orientationName: '南',
        address: '上海市嘉定区祁连路1255号',
        price: '1550',
        image: 'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
        tag: '保租房',
      },
      {
        id: 1,
        title: '整租·精品一居·菊园社区',
        area: '28.9',
        floor: 9,
        totalFloor: 11,
        orientationName: '南',
        address: '上海市嘉定区祁连路1255号',
        price: '1550',
        image: 'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
        tag: '保租房',
      },
      {
        id: 2,
        title: '整租·精品一居·菊园社区',
        area: '28.9',
        floor: 9,
        totalFloor: 11,
        orientationName: '南',
        address: '上海市嘉定区祁连路1255号',
        price: '1550',
        image: 'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
        tag: '保租房',
      },
      {
        id: 1,
        title: '整租·精品一居·菊园社区',
        area: '28.9',
        floor: 9,
        totalFloor: 11,
        orientationName: '南',
        address: '上海市嘉定区祁连路1255号',
        price: '1550',
        image: 'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
        tag: '保租房',
      },
      {
        id: 2,
        title: '整租·精品一居·菊园社区',
        area: '28.9',
        floor: 9,
        totalFloor: 11,
        orientationName: '南',
        address: '上海市嘉定区祁连路1255号',
        price: '1550',
        image: 'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
        tag: '保租房',
      },
      {
        id: 1,
        title: '整租·精品一居·菊园社区',
        area: '28.9',
        floor: 9,
        totalFloor: 11,
        orientationName: '南',
        address: '上海市嘉定区祁连路1255号',
        price: '1550',
        image: 'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
        tag: '保租房',
      },
      {
        id: 2,
        title: '整租·精品一居·菊园社区',
        area: '28.9',
        floor: 9,
        totalFloor: 11,
        orientationName: '南',
        address: '上海市嘉定区祁连路1255号',
        price: '1550',
        image: 'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
        tag: '保租房',
      },
      {
        id: 1,
        title: '整租·精品一居·菊园社区',
        area: '28.9',
        floor: 9,
        totalFloor: 11,
        orientationName: '南',
        address: '上海市嘉定区祁连路1255号',
        price: '1550',
        image: 'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
        tag: '保租房',
      },
      {
        id: 2,
        title: '整租·精品一居·菊园社区',
        area: '28.9',
        floor: 9,
        totalFloor: 11,
        orientationName: '南',
        address: '上海市嘉定区祁连路1255号',
        price: '1550',
        image: 'https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png',
        tag: '保租房',
      },
    ])
  }, 200)
}

onMounted(() => {
  getNavTopHeight()
})
</script>

<style lang="scss" scoped>
.find-page-top {
  position: relative;
  z-index: 100;
  background: #ffffff;
  &-box {
    padding: 0 30rpx;
    .search-header {
      width: 100%;
      display: flex;
      align-items: center;
      border: 1rpx solid #dbdbdb;
      border-radius: 33rpx;
      height: 66rpx;

      .search-icon {
        width: 26rpx;
        height: 26rpx;
        margin-left: 30rpx;
      }

      .search-input {
        flex: 1;
        height: 100%;
        font-size: 26rpx;
        color: #000000;
        margin-left: 22rpx;

        &::placeholder {
          color: #999999;
        }
      }

      .search-btn {
        padding: 0 22rpx;
        color: #8cc224;
        font-size: 26rpx;
        border-left: 1rpx solid #dbdbdb;
        margin-left: 20rpx;
      }
    }
    .setion {
      width: 100%;
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      &-item {
        display: flex;
        align-items: center;
        justify-content: center;
        text {
          font-size: 26rpx;
          color: #333333;
          &.active {
            color: #8cc224;
          }
        }
        .icon {
          width: 18rpx;
          height: 18rpx;
          margin-left: 10rpx;
        }
      }
      .change-icon {
        width: 36rpx;
        height: 36rpx;
      }
    }
  }
  .line {
    width: 100%;
    height: 10rpx;
    background: #f8f8f8;
  }
}
.list {
  width: 100%;
  padding: 20rpx 30rpx 0;
  box-sizing: border-box;
  &-item {
    margin-bottom: 30rpx;
    &:last-of-type {
      margin-bottom: 0;
    }
  }
}
</style>
