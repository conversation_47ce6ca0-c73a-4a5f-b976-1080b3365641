<template>
  <view class="apartment-page">
    <view class="apartment-page-top">
      <CustomNavbar title="公寓" :show-back="false" />
      <view class="setion">
        <view class="setion-item" v-for="item in setionList" :key="item.value">
          <text>{{ item.label }}</text>
          <image class="icon" src="@/static/icon/choose-close.png"></image>
        </view>
      </view>
    </view>
    <scroll-view class="scroll-list" scroll-y :style="{ height: `calc(100vh - ${topHeight}px)` }">
      <view class="list-item" v-for="item in [1, 2]" :key="item">
        <image class="img" src="https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png"></image>
        <view class="list-item-details">
          <text class="list-item-name">剧院社区</text>
          <view class="price-info">
            <text class="price"><text class="price-unit">¥</text>12.21</text>
            <text class="price-unit">/月起</text>
          </view>
        </view>
        <view class="list-item-bottom">
          <image class="icon" src="@/static/icon/address-icon.png"></image>
          <text class="list-item-address">杨浦区国权北路1688弄</text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import CustomNavbar from '@/components/navbar/index.vue'
import { onMounted } from 'vue';

const setionList = [
  {
    label: '位置',
    value: 'location',
    list: ['不限', '距离从近到远', '距离从远到近'],
  },
  {
    label: '租金',
    value: 'price',
    list: ['不限', '租金从低到高', '租金从高到低'],
  },
]

onMounted(() => {
  
})
</script>

<style lang="scss" scoped>
.apartment-page {
  min-height: 100vh;
  background-color: #f8f8f8;
  &-top {
    .setion {
      width: 100%;
      height: 80rpx;
      background: #ffffff;
      display: flex;
      &-item {
        width: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        text {
          font-size: 26rpx;
          color: #333333;
        }
        .icon {
          width: 18rpx;
          height: 18rpx;
          margin-left: 10rpx;
        }
      }
    }
  }
}
</style>
