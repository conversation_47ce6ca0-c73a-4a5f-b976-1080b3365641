<template>
  <view class="apartment-page">
    <view class="header">
      <text class="title">公寓</text>
    </view>
    <view class="content">
      <view class="apartment-list">
        <text class="placeholder">公寓列表加载中...</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 页面数据
const apartmentList = ref([])
const loading = ref(true)

// 加载公寓列表
const loadApartments = async () => {
  try {
    loading.value = true
    // TODO: 调用API获取公寓列表
    console.log('加载公寓列表')
  } catch (error) {
    console.error('加载公寓列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 页面加载时执行
onMounted(() => {
  console.log('公寓页面加载完成')
  loadApartments()
})
</script>

<style lang="scss" scoped>
.apartment-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  
  .header {
    padding: 20rpx;
    background-color: #fff;
    text-align: center;
    
    .title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }
  }
  
  .content {
    padding: 20rpx;
    
    .apartment-list {
      background-color: #fff;
      border-radius: 16rpx;
      padding: 40rpx 20rpx;
      text-align: center;
      
      .placeholder {
        font-size: 28rpx;
        color: #999;
      }
    }
  }
}
</style>
