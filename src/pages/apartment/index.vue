<template>
  <z-paging ref="paging" v-model="dataList">
    <template #top>
      <view class="apartment-page-top">
        <CustomNavbar title="公寓" :show-back="false" />
        <view class="setion">
          <view class="setion-item" v-for="item in setionList" :key="item.value" @click="chooseSetion(item)">
            <text :class="{ active: item.chooseValue }">{{ item.label }}</text>
            <image v-if="item.isCurrent" class="icon" src="@/static/icon/choose-open.png"></image>
            <image v-else class="icon" src="@/static/icon/choose-close.png"></image>
          </view>
        </view>
      </view>
    </template>
    <view class="list">
      <view class="list-item" v-for="item in dataList" :key="item">
        <image
          class="img"
          mode="aspectFill"
          src="https://yuxin-test.oss-cn-hangzhou.aliyuncs.com/2025-04-21/6a5fcaacf9e346f6af22f5adafa75638.png"
        ></image>
        <view class="list-item-bottom">
          <view class="list-item-bottom-details">
            <text class="name">剧院社区</text>
            <view class="price-info">
              <text class="price"><text class="price-unit">¥</text>12.21</text>
              <text class="price-unit">/月起</text>
            </view>
          </view>
          <view class="address-info">
            <image class="icon" src="@/static/icon/address-icon.png"></image>
            <text>杨浦区国权北路1688弄</text>
          </view>
        </view>
      </view>
    </view>
    <template slot="empty">
      <view class="empty"> 空 </view>
    </template>
  </z-paging>
  <ChoosePopup ref="choosePopupRef" :top="topHeight" :setionList="setionList" />
  <Tabbar />
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, onMounted, ref } from 'vue'

import CustomNavbar from '@/components/navbar/index.vue'
import Tabbar from '@/components/tabbar/index.vue'

import { getElementRect, sleep } from '@/utils'

import ChoosePopup from './components/ChoosePopup.vue'

const instance = getCurrentInstance()
const topHeight = ref(0)
const choosePopupRef = ref()
const paging = ref()
const dataList = ref([1, 2, 3, 4, 5])
const setionList = ref([
  {
    label: '位置',
    value: 'location',
    list: [
      {
        label: '不限',
        value: 'buxian',
      },
      {
        label: '距离从近到远',
        value: 'jin',
      },
      {
        label: '距离从远到近',
        value: 'yuan',
      },
    ],
    isCurrent: false,
    chooseValue: '',
  },
  {
    label: '租金',
    value: 'price',
    list: [
      {
        label: '不限',
        value: 'buxian',
      },
      {
        label: '租金从低到高',
        value: 'di',
      },
      {
        label: '租金从高到低',
        value: 'gao',
      },
    ],
    isCurrent: false,
    chooseValue: '',
  },
])

const getNavTopHeight = async () => {
  await sleep()
  const res = await getElementRect('.apartment-page-top', instance)
  topHeight.value = res.height
}

const chooseSetion = (item) => {
  if (item.isCurrent) {
    item.isCurrent = false
    choosePopupRef.value.close()
    return
  }

  setionList.value.forEach((v) => {
    v.isCurrent = v.value === item.value
  })
  choosePopupRef.value.open()
}

// @query所绑定的方法不要自己调用！！需要刷新列表数据时，只需要调用paging.value.reload()即可
// const queryList = (pageNo, pageSize) => {
//   // 此处请求仅为演示，请替换为自己项目中的请求
//   request
//     .queryList({ pageNo, pageSize })
//     .then((res) => {
//       // 将请求结果通过complete传给z-paging处理，同时也代表请求结束，这一行必须调用
//       paging.value.complete(res.data.list)
//     })
//     .catch((res) => {
//       // 如果请求失败写paging.value.complete(false);
//       // 注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
//       // 在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
//       paging.value.complete(false)
//     })
// }

onMounted(() => {
  getNavTopHeight()
})
</script>

<style lang="scss" scoped>
.apartment-page-top {
  // position: relative;
  // z-index: 100;
  .setion {
    width: 100%;
    height: 80rpx;
    background: #ffffff;
    display: flex;
    &-item {
      width: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      text {
        font-size: 26rpx;
        color: #333333;
        &.active {
          color: #8cc224;
        }
      }
      .icon {
        width: 18rpx;
        height: 18rpx;
        margin-left: 10rpx;
      }
    }
  }
}
.list {
  width: 100%;
  padding: 20rpx 30rpx 0;
  box-sizing: border-box;
  background: #f8f8f8;
  .list-item {
    width: 100%;
    background: #ffffff;
    border-radius: 16rpx;
    overflow: hidden;
    margin-bottom: 20rpx;
    .img {
      width: 100%;
      height: 330rpx;
      border-radius: 16rpx;
    }
    &-bottom {
      padding: 24rpx 20rpx;
      background: #ffffff;
      &-details {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .name {
          font-weight: bold;
          font-size: 28rpx;
          color: #000000;
        }
        .price-info {
          display: flex;
          align-items: baseline;
          gap: 5rpx;
          color: #8cc224;

          .price {
            font-size: 36rpx;
            font-weight: bold;
          }

          .price-unit {
            font-size: 24rpx;
          }
        }
      }
      .address-info {
        display: flex;
        align-items: center;
        margin-top: 20rpx;

        .icon {
          width: 24rpx;
          height: 24rpx;
        }

        text {
          font-size: 24rpx;
          color: #666666;
          display: block;
          margin-left: 12rpx;
        }
      }
    }
  }
}
</style>
