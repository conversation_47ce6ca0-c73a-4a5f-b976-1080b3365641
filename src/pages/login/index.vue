<template>
  <view class="container" :style="{ background: `url(${imgUrl}/login-bg.png) no-repeat`, backgroundSize: 'cover' }">
    <CustomNavbar title="登录" background="transparent" />
    <view class="content">
      <view class="logo">
        <image class="logo-img" :src="`${imgUrl}/login-logo.jpg`"></image>
      </view>
      <button v-if="isSelect" class="phone-btn" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">
        手机号一键登录
      </button>
      <button v-else class="phone-btn" @click="checkPhone">手机号一键登录</button>
      <button class="code-btn" @click="loginBySmsMethod">验证码登录</button>
    </view>
    <view class="assignment" @click="checkSelect">
      <image v-if="isSelect" class="icon" src="@/static/icon/selected.png"></image>
      <image v-else class="icon" src="@/static/icon/no-select.png"></image>
      <view v-if="showTip" class="tip">请勾选同意后再进行登录</view>
      <text>我已阅读并同意<text class="primary">《用户协议》</text>和<text class="primary">《隐私政策》</text></text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

import CustomNavbar from '@/components/navbar/index.vue'

import { imgUrl } from '@/utils/request'

const isSelect = ref(false)
const showTip = ref(false)

// 获取手机号授权
const getPhoneNumber = (e: any) => {
  console.log('获取手机号:', e)
  if (e.detail.errMsg === 'getPhoneNumber:ok') {
    // 获取到手机号授权，进行登录
    handlePhoneLogin(e.detail)
  } else {
    uni.showToast({
      title: '获取手机号失败',
      icon: 'none',
    })
  }
}

// 检查手机号授权
const checkPhone = () => {
  if (!isSelect.value) {
    showTip.value = true
    setTimeout(() => {
      showTip.value = false
    }, 2000)
    return
  }

  uni.showToast({
    title: '请先勾选同意协议',
    icon: 'none',
  })
}

// 手机号登录处理
const handlePhoneLogin = async (phoneData: any) => {
  try {
    // TODO: 调用登录API
    console.log('手机号登录:', phoneData)

    uni.showToast({
      title: '登录成功',
      icon: 'success',
    })

    // 登录成功后跳转
    setTimeout(() => {
      uni.switchTab({
        url: '/pages/index/index',
      })
    }, 1500)
  } catch (error) {
    console.error('登录失败:', error)
    uni.showToast({
      title: '登录失败',
      icon: 'none',
    })
  }
}

// 验证码登录
const loginBySmsMethod = () => {
  uni.navigateTo({
    url: '/pages/login/sms',
  })
}

// 切换协议选择状态
const checkSelect = () => {
  isSelect.value = !isSelect.value
  if (showTip.value) {
    showTip.value = false
  }
}
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100vh;
  position: relative;

  .content {
    width: 100%;
    padding: 200rpx 96rpx 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-sizing: border-box;
  }

  .logo {
    width: 180rpx;
    height: 180rpx;
    border-radius: 34rpx;
    background: #fefefe;
    box-shadow: 0rpx 3rpx 6rpx 1rpx rgba(0, 168, 207, 0.24);
    display: flex;
    align-items: center;
    justify-content: center;
    padding-left: 6rpx;
    box-sizing: border-box;

    .logo-img {
      width: 150rpx;
      height: 150rpx;
    }
  }

  .phone-btn {
    width: 100%;
    height: 96rpx;
    line-height: 96rpx;
    background: #00a8cf;
    border-radius: 16rpx;
    font-size: 28rpx;
    color: #f8f8f8;
    margin-top: 110rpx;
    border: none;

    &::after {
      border: none;
    }
  }

  .code-btn {
    width: 100%;
    height: 96rpx;
    line-height: 96rpx;
    background: #ffffff;
    border-radius: 16rpx;
    font-size: 28rpx;
    color: #00a8cf;
    margin-top: 30rpx;
    border: 1px solid #00a8cf;

    &::after {
      border: none;
    }
  }
}

.assignment {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  bottom: calc(env(safe-area-inset-bottom) + 38rpx);
  left: 50%;
  transform: translateX(-50%);
  width: 100%;

  .icon {
    width: 24rpx;
    height: 24rpx;
    margin-right: 10rpx;
  }

  text {
    font-size: 22rpx;
    color: #999999;

    .primary {
      color: #00a8cf;
    }
  }

  .tip {
    background: rgba(0, 0, 0, 0.5);
    font-size: 22rpx;
    color: #f8f8f8;
    padding: 20rpx 34rpx;
    border-radius: 8rpx;
    position: absolute;
    left: 62rpx;
    top: -94rpx;

    &::before {
      content: '';
      position: absolute;
      width: 0;
      height: 0;
      border-left: 10rpx solid transparent;
      border-right: 10rpx solid transparent;
      border-top: 10rpx solid rgba(0, 0, 0, 0.5);
      top: 100%;
      left: 78rpx;
    }
  }
}
</style>
