<template>
  <view class="mine-page">
    <!-- 背景 -->
    <image class="mine-background" :src="`${imgUrl}/mine-bg.png`"></image>

    <view class="mine-page-container">
      <view class="mine-page-container-top">
        <!-- 自定义导航栏 -->
        <CustomNavbar title="我的" :show-back="false" background-color="transparent" />

        <!-- 用户信息区域 -->
        <view class="user-section">
          <view class="user-info">
            <view class="user-details">
              <text class="phone-number">{{ state.userInfo.phone || '请登录' }}</text>
              <text class="user-desc">{{ state.userInfo.desc || '注册手机号不会透漏给别人' }}</text>
            </view>
            <image class="avatar" :src="`${imgUrl}/mine-avatar.png`"></image>
          </view>

          <!-- 统计信息 -->
          <view class="stats-section">
            <view class="stat-item" v-for="item in state.statsList" :key="item.key" @click="handleClick(item)">
              <image class="stat-icon" :src="item.icon"></image>
              <text class="stat-text">{{ item.name }}({{ item.num }})</text>
            </view>
          </view>
        </view>
      </view>

      <view class="mine-page-container-bottom">
        <!-- banner -->
        <view class="mine-banner">
          <image class="mine-banner-img" mode="widthFix" :src="`${imgUrl}/mine-banner.png`"></image>
        </view>

        <!-- 我的家功能区 -->
        <view class="my-home-section">
          <view class="section-header">
            <text class="section-title">我的家()</text>
            <view class="switch-source" @click="switchSource">
              <text class="switch-text">切换房源</text>
              <image class="switch-arrow" src="@/static/icon/right.png"></image>
            </view>
          </view>

          <!-- 功能网格 -->
          <view class="function-grid">
            <view class="function-item" v-for="item in state.functionList" :key="item.key" @click="handleClick(item)">
              <image class="function-icon" :src="item.icon"></image>
              <text class="function-text">{{ item.name }}</text>
            </view>
          </view>
        </view>

        <!-- 退出登录按钮 -->
        <view class="logout-section">
          <button class="logout-btn" @click="handleLogout">退出登录</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'

import CustomNavbar from '@/components/navbar/index.vue'

import { imgUrl } from '@/utils/request'

const state = reactive({
  userInfo: {
    phone: '18878786765',
    desc: '注册手机号不会透漏给别人',
    avatar: '',
  },
  statsList: [
    {
      key: 'collection',
      name: '收藏',
      num: 0,
      icon: `${imgUrl}/mine-collection.png`,
      handler() {},
    },
    {
      key: 'appointment',
      name: '约看',
      num: 0,
      icon: `${imgUrl}/mine-appointment.png`,
      handler() {},
    },
    {
      key: 'reserve',
      name: '预定',
      num: 0,
      icon: `${imgUrl}/mine-reserve.png`,
      handler() {},
    },
  ],
  functionList: [
    {
      key: 'contract',
      name: '合同',
      icon: `${imgUrl}/mine-contract.png`,
      handler() {},
    },
    {
      key: 'bill',
      name: '账单',
      icon: `${imgUrl}/mine-bill.png`,
      handler() {},
    },
    {
      key: 'lock',
      name: '智能锁',
      icon: `${imgUrl}/mine-lock.png`,
      handler() {},
    },
    {
      key: 'access',
      name: '门禁',
      icon: `${imgUrl}/mine-access.png`,
      handler() {},
    },
    {
      key: 'butler',
      name: '管家',
      icon: `${imgUrl}/mine-butler.png`,
      handler() {},
    },
    {
      key: 'maintenance',
      name: '维修',
      icon: `${imgUrl}/mine-maintenance.png`,
      handler() {},
    },
    {
      key: 'cleaning',
      name: '保洁',
      icon: `${imgUrl}/mine-cleaning.png`,
      handler() {},
    },
    {
      key: 'complaint',
      name: '投诉',
      icon: `${imgUrl}/mine-complaint.png`,
      handler() {},
    },
  ],
})

const handleClick = (item) => {
  item.handler && item.handler()
}

// 切换房源
const switchSource = () => {
  uni.navigateTo({
    url: '/pages/house-list/index',
  })
}

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        // TODO: 清除用户信息和token
        uni.showToast({
          title: '已退出登录',
          icon: 'success',
        })

        // 跳转到登录页面
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/login/index',
          })
        }, 1500)
      }
    },
  })
}

// 加载用户信息
const loadUserInfo = async () => {
  try {
    // TODO: 调用API获取用户信息
    console.log('加载用户信息')
  } catch (error) {
    console.error('加载用户信息失败:', error)
  }
}

// 加载统计数据
const loadStats = async () => {
  try {
    // TODO: 调用API获取统计数据
    console.log('加载统计数据')
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 页面加载时执行
onMounted(() => {
  loadUserInfo()
  loadStats()
})
</script>

<style lang="scss" scoped>
.mine-page {
  min-height: 100vh;
  background-color: #ffffff;
  position: relative;

  .mine-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 600rpx;
    z-index: 1;
  }

  &-container {
    position: relative;
    z-index: 2;

    &-bottom {
      background: #ffffff;
      border-radius: 20rpx 20rpx 0rpx 0rpx;
      margin-top: 30rpx;
      padding-top: 30rpx;
    }
  }
}

.user-section {
  margin-top: 60rpx;
  padding-left: 30rpx;
  padding-right: 40rpx;
}

.user-info {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .user-details {
    flex: 1;

    .phone-number {
      display: block;
      font-size: 34rpx;
      font-weight: bold;
      color: #000000;
    }

    .user-desc {
      font-size: 26rpx;
      color: #707070;
      margin-top: 28rpx;
      display: block;
    }
  }

  .avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
  }
}

.stats-section {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-top: 50rpx;

  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;

    .stat-icon {
      width: 50rpx;
      height: 50rpx;
    }

    .stat-text {
      font-size: 26rpx;
      color: #707070;
      margin-top: 16rpx;
    }
  }
}

.mine-banner {
  padding: 0 30rpx;

  &-img {
    width: 100%;
  }
}

.my-home-section {
  margin-top: 50rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;

  .section-title {
    font-size: 34rpx;
    font-weight: bold;
    color: #000000;
  }

  .switch-source {
    display: flex;
    align-items: center;

    .switch-text {
      font-size: 24rpx;
      color: #666666;
    }

    .switch-arrow {
      width: 26rpx;
      height: 26rpx;
      margin-left: 4rpx;
    }
  }
}

.function-grid {
  display: flex;
  flex-wrap: wrap;

  .function-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 40rpx;
    width: 25%;

    .function-icon {
      width: 60rpx;
      height: 60rpx;
    }

    .function-text {
      font-size: 26rpx;
      color: #333333;
      margin-top: 16rpx;
    }
  }
}

.logout-section {
  margin: 70rpx 30rpx;

  .logout-btn {
    width: 100%;
    height: 88rpx;
    line-height: 88rpx;
    background-color: #00a8cf;
    color: #ffffff;
    border: none;
    border-radius: 16rpx;
    font-size: 28rpx;
    font-weight: 400;

    &::after {
      border: none;
    }
  }
}
</style>
