<template>
  <view class="mine-page">
    <CustomNavbar title="我的" :show-back="false" background-color="#ffffff" title-color="#333333" />
    <view class="user-info">
      <view class="avatar">
        <image src="/static/default-avatar.png" class="avatar-img" mode="aspectFill" />
      </view>
      <view class="user-details">
        <text class="username">{{ userInfo.username || '未登录' }}</text>
        <text class="phone">{{ userInfo.phone || '请登录' }}</text>
      </view>
    </view>
    <view class="menu-list">
      <view class="menu-item" v-for="item in menuItems" :key="item.id" @click="handleMenuClick(item)">
        <view class="menu-icon">
          <text class="icon">{{ item.icon }}</text>
        </view>
        <text class="menu-text">{{ item.title }}</text>
        <text class="arrow">></text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'

import CustomNavbar from '@/components/navbar/index.vue'

// 用户信息
const userInfo = ref({
  username: '',
  phone: '',
  avatar: '',
})

// 菜单项
const menuItems = ref([
  { id: 1, title: '我的收藏', icon: '♥', path: '/pages/favorite/index' },
  { id: 2, title: '我的预约', icon: '📅', path: '/pages/appointment/index' },
  { id: 3, title: '我的合同', icon: '📄', path: '/pages/contract/index' },
  { id: 4, title: '设置', icon: '⚙', path: '/pages/settings/index' },
])

// 菜单点击处理
const handleMenuClick = (item: any) => {
  console.log('点击菜单:', item.title)
  // TODO: 跳转到对应页面
  // uni.navigateTo({
  //   url: item.path
  // })
}

// 加载用户信息
const loadUserInfo = async () => {
  try {
    // TODO: 调用API获取用户信息
    console.log('加载用户信息')
  } catch (error) {
    console.error('加载用户信息失败:', error)
  }
}

// 页面加载时执行
onMounted(() => {
  console.log('我的页面加载完成')
  loadUserInfo()
})
</script>

<style lang="scss" scoped>
.mine-page {
  min-height: 100vh;
  background-color: #f5f5f5;

  .user-info {
    display: flex;
    align-items: center;
    padding: 40rpx 20rpx;
    background-color: #fff;
    margin-bottom: 20rpx;

    .avatar {
      margin-right: 30rpx;

      .avatar-img {
        width: 120rpx;
        height: 120rpx;
        border-radius: 60rpx;
        background-color: #f0f0f0;
      }
    }

    .user-details {
      flex: 1;

      .username {
        display: block;
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 10rpx;
      }

      .phone {
        font-size: 28rpx;
        color: #666;
      }
    }
  }

  .menu-list {
    background-color: #fff;

    .menu-item {
      display: flex;
      align-items: center;
      padding: 30rpx 20rpx;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .menu-icon {
        margin-right: 20rpx;

        .icon {
          font-size: 32rpx;
        }
      }

      .menu-text {
        flex: 1;
        font-size: 30rpx;
        color: #333;
      }

      .arrow {
        font-size: 24rpx;
        color: #999;
      }
    }
  }
}
</style>
