<template>
  <view class="home-page">
    <!-- 顶部背景区域 -->
    <view class="header-section">
      <!-- 状态栏占位 -->
      <view class="status-bar"></view>

      <!-- 背景装饰 -->
      <view class="header-bg">
        <image src="/static/images/header-decoration.png" class="decoration-img" mode="aspectFill" />
      </view>

      <!-- 顶部内容 -->
      <view class="header-content">
        <!-- 标题 -->
        <view class="title-section">
          <text class="main-title">建悦里租房</text>
          <view class="nav-icons">
            <text class="nav-icon">⋯</text>
            <text class="nav-icon">◯</text>
          </view>
        </view>

        <!-- 搜索区域 -->
        <view class="search-section">
          <view class="location-selector" @click="selectLocation">
            <text class="location-text">{{ currentLocation }}</text>
            <text class="dropdown-icon">▼</text>
          </view>

          <view class="search-bar" @click="goToSearch">
            <text class="search-icon">🔍</text>
            <text class="search-placeholder">请输入小区名称开始找房</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- 推荐门店 -->
      <view class="section">
        <view class="section-header" @click="goToStoreList">
          <text class="section-title">推荐门店</text>
          <text class="more-icon">></text>
        </view>

        <scroll-view
          class="store-scroll"
          scroll-x="true"
          show-scrollbar="false"
          enhanced="true"
          :scroll-with-animation="true"
        >
          <view class="store-list">
            <view
              v-for="(store, index) in storeList"
              :key="index"
              class="store-card"
              @click="goToStoreDetail(store.id)"
            >
              <view class="store-images">
                <image :src="store.mainImage" class="main-image" mode="aspectFill" />
                <view class="sub-images">
                  <image
                    v-for="(img, imgIndex) in store.subImages"
                    :key="imgIndex"
                    :src="img"
                    class="sub-image"
                    mode="aspectFill"
                  />
                </view>
              </view>

              <view class="store-info">
                <text class="store-name">{{ store.name }}</text>
                <view class="store-details">
                  <text class="location-icon">📍</text>
                  <text class="store-address">{{ store.address }}</text>
                </view>
                <view class="price-info">
                  <text class="price">¥{{ store.price }}</text>
                  <text class="price-unit">/月起</text>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 精选房间 -->
      <view class="section">
        <view class="section-header" @click="goToRoomList">
          <text class="section-title">精选房间</text>
          <text class="more-icon">></text>
        </view>

        <view class="room-list">
          <RoomCard v-for="(room, index) in roomList" :key="index" :room-data="room" @click="goToRoomDetail" />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'

import { RoomCard } from '@/components'

// 当前位置
const currentLocation = ref('上海市')

// 门店列表
const storeList = ref([
  {
    id: 1,
    name: '菊园社区',
    address: '杨浦区国权北路1688弄',
    price: '1980',
    mainImage: '/static/images/store-main.jpg',
    subImages: ['/static/images/store-sub1.jpg', '/static/images/store-sub2.jpg'],
  },
  {
    id: 2,
    name: '绿地社区',
    address: '浦东新区张江路2000弄',
    price: '2200',
    mainImage: '/static/images/store-main2.jpg',
    subImages: ['/static/images/store-sub3.jpg', '/static/images/store-sub4.jpg'],
  },
  {
    id: 3,
    name: '万科城市花园',
    address: '徐汇区漕河泾开发区',
    price: '2800',
    mainImage: '/static/images/store-main3.jpg',
    subImages: ['/static/images/store-sub5.jpg', '/static/images/store-sub6.jpg'],
  },
])

// 房间列表
const roomList = ref([
  {
    id: 1,
    title: '整租·精品一居·菊园社区',
    size: '28.9㎡',
    type: '9/11层 | 朝南',
    address: '上海市嘉定区祁连路1255号',
    price: '1550',
    image: '/static/images/room1.jpg',
    tags: ['保租房'],
  },
  {
    id: 2,
    title: '整租·精品一居·菊园社区',
    size: '28.9㎡',
    type: '9/11层 | 朝南',
    address: '上海市嘉定区祁连路1255号',
    price: '1550',
    image: '/static/images/room2.jpg',
    tags: ['保租房'],
  },
])

// 选择位置
const selectLocation = () => {
  uni.showActionSheet({
    itemList: ['上海市', '北京市', '广州市', '深圳市'],
    success: (res) => {
      const locations = ['上海市', '北京市', '广州市', '深圳市']
      currentLocation.value = locations[res.tapIndex]
    },
  })
}

// 跳转到搜索页面
const goToSearch = () => {
  uni.navigateTo({
    url: '/pages/search/index',
  })
}

// 跳转到门店列表
const goToStoreList = () => {
  uni.navigateTo({
    url: '/pages/store-list/index',
  })
}

// 跳转到门店详情
const goToStoreDetail = (storeId: number) => {
  uni.navigateTo({
    url: `/pages/store-detail/index?id=${storeId}`,
  })
}

// 跳转到房间列表
const goToRoomList = () => {
  uni.navigateTo({
    url: '/pages/room-list/index',
  })
}

// 跳转到房间详情
const goToRoomDetail = (roomId: number) => {
  uni.navigateTo({
    url: `/pages/room-detail/index?id=${roomId}`,
  })
}

// 加载数据
const loadData = async () => {
  try {
    // TODO: 调用API获取门店和房间数据
    console.log('加载首页数据')
  } catch (error) {
    console.error('加载数据失败:', error)
  }
}

// 页面加载时执行
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.home-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header-section {
  position: relative;
  height: 400rpx;
  background: linear-gradient(135deg, #a8e6cf 0%, #dcedc1 100%);
  overflow: hidden;
}

.status-bar {
  height: 88rpx;
}

.header-bg {
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 1;

  .decoration-img {
    width: 100%;
    height: 100%;
    opacity: 0.3;
  }
}

.header-content {
  position: relative;
  z-index: 2;
  padding: 0 30rpx;
  height: calc(100% - 88rpx);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.title-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 20rpx;

  .main-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333333;
  }

  .nav-icons {
    display: flex;
    gap: 20rpx;

    .nav-icon {
      font-size: 32rpx;
      color: #333333;
    }
  }
}

.search-section {
  padding-bottom: 40rpx;

  .location-selector {
    display: flex;
    align-items: center;
    gap: 10rpx;
    margin-bottom: 20rpx;

    .location-text {
      font-size: 28rpx;
      color: #333333;
      font-weight: 500;
    }

    .dropdown-icon {
      font-size: 20rpx;
      color: #666666;
    }
  }

  .search-bar {
    display: flex;
    align-items: center;
    gap: 20rpx;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 50rpx;
    padding: 20rpx 30rpx;
    backdrop-filter: blur(10rpx);

    .search-icon {
      font-size: 32rpx;
      color: #999999;
    }

    .search-placeholder {
      font-size: 28rpx;
      color: #999999;
      flex: 1;
    }
  }
}

.main-content {
  padding: 30rpx 20rpx;
}

.section {
  margin-bottom: 40rpx;

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333333;
    }

    .more-icon {
      font-size: 24rpx;
      color: #999999;
    }
  }
}

.store-scroll {
  width: 100%;
  white-space: nowrap;

  .store-list {
    display: flex;
    gap: 20rpx;
    padding: 0 20rpx;

    .store-card {
      flex-shrink: 0;
      width: 600rpx;
      background-color: #ffffff;
      border-radius: 16rpx;
      overflow: hidden;
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

      &:first-child {
        margin-left: 0;
      }

      &:last-child {
        margin-right: 20rpx;
      }

      .store-images {
        display: flex;
        height: 300rpx;

        .main-image {
          flex: 2;
          height: 100%;
        }

        .sub-images {
          flex: 1;
          display: flex;
          flex-direction: column;

          .sub-image {
            flex: 1;
            height: 50%;
          }
        }
      }

      .store-info {
        padding: 30rpx;

        .store-name {
          font-size: 32rpx;
          font-weight: bold;
          color: #333333;
          margin-bottom: 15rpx;
        }

        .store-details {
          display: flex;
          align-items: center;
          gap: 10rpx;
          margin-bottom: 15rpx;

          .location-icon {
            font-size: 24rpx;
            color: #666666;
          }

          .store-address {
            font-size: 24rpx;
            color: #666666;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .price-info {
          display: flex;
          align-items: baseline;
          gap: 5rpx;

          .price {
            font-size: 36rpx;
            font-weight: bold;
            color: #ff6b35;
          }

          .price-unit {
            font-size: 24rpx;
            color: #999999;
          }
        }
      }
    }
  }
}
</style>
