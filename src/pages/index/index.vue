<template>
  <view class="index-page">
    <view class="header">
      <text class="title">首页</text>
    </view>
    <view class="content">
      <text class="welcome">欢迎来到建悦里</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 页面数据
const pageData = ref({
  title: '首页'
})

// 页面加载时执行
onMounted(() => {
  console.log('首页加载完成')
})
</script>

<style lang="scss" scoped>
.index-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  
  .header {
    padding: 20rpx;
    background-color: #fff;
    text-align: center;
    
    .title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }
  }
  
  .content {
    padding: 40rpx 20rpx;
    text-align: center;
    
    .welcome {
      font-size: 32rpx;
      color: #666;
    }
  }
}
</style>
