<template>
  <view class="demo-page">
    <!-- 示例1: 基础用法 -->
    <view class="demo-section">
      <CustomNavbar 
        title="基础导航栏" 
        @back="handleBack"
      />
      <view class="demo-content">
        <text class="demo-title">基础用法示例</text>
        <text class="demo-desc">默认白色背景，显示返回按钮</text>
      </view>
    </view>

    <!-- 示例2: 自定义颜色 -->
    <view class="demo-section">
      <CustomNavbar 
        title="自定义颜色导航栏"
        background-color="#007aff"
        title-color="#ffffff"
        :border-bottom="false"
        @back="handleBack"
      />
      <view class="demo-content">
        <text class="demo-title">自定义颜色示例</text>
        <text class="demo-desc">蓝色背景，白色标题，无底部边框</text>
      </view>
    </view>

    <!-- 示例3: 渐变背景 -->
    <view class="demo-section">
      <CustomNavbar 
        title="渐变背景导航栏"
        title-color="#ffffff"
        :custom-style="{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
        }"
        @back="handleBack"
      />
      <view class="demo-content">
        <text class="demo-title">渐变背景示例</text>
        <text class="demo-desc">使用 customStyle 设置渐变背景</text>
      </view>
    </view>

    <!-- 示例4: 不显示返回按钮 -->
    <view class="demo-section">
      <CustomNavbar 
        title="无返回按钮"
        :show-back="false"
        background-color="#f8f8f8"
        title-color="#333333"
      />
      <view class="demo-content">
        <text class="demo-title">无返回按钮示例</text>
        <text class="demo-desc">适用于首页或不需要返回的页面</text>
      </view>
    </view>

    <!-- 示例5: 自定义返回图标 -->
    <view class="demo-section">
      <CustomNavbar 
        title="自定义返回图标"
        back-icon="/images/icon/back.png"
        background-color="#50c878"
        title-color="#ffffff"
        @back="handleCustomBack"
      />
      <view class="demo-content">
        <text class="demo-title">自定义返回图标示例</text>
        <text class="demo-desc">使用自定义图标和返回逻辑</text>
      </view>
    </view>

    <!-- 示例6: 使用插槽 -->
    <view class="demo-section">
      <CustomNavbar 
        title="插槽自定义"
        background-color="#ff6b6b"
        title-color="#ffffff"
      >
        <template #left>
          <view class="custom-left" @click="showMenu">
            <text class="menu-text">☰</text>
          </view>
        </template>
        
        <template #right>
          <view class="custom-right">
            <text class="share-btn" @click="shareContent">分享</text>
            <text class="more-btn" @click="showMore">⋯</text>
          </view>
        </template>
      </CustomNavbar>
      <view class="demo-content">
        <text class="demo-title">插槽自定义示例</text>
        <text class="demo-desc">左侧菜单按钮，右侧分享和更多按钮</text>
      </view>
    </view>

    <!-- 示例7: 固定定位 -->
    <view class="demo-section">
      <CustomNavbar 
        ref="fixedNavbar"
        title="固定定位导航栏"
        :fixed="true"
        background-color="#4a90e2"
        title-color="#ffffff"
        :z-index="1000"
        @back="handleBack"
      />
      <view class="demo-content" :style="{ paddingTop: fixedNavbarHeight + 'px' }">
        <text class="demo-title">固定定位示例</text>
        <text class="demo-desc">导航栏固定在顶部，页面内容可滚动</text>
        <view class="scroll-content">
          <view v-for="i in 20" :key="i" class="scroll-item">
            <text>滚动内容项 {{ i }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import CustomNavbar from '@/components/navbar/index.vue'

// 固定导航栏引用和高度
const fixedNavbar = ref()
const fixedNavbarHeight = ref(88)

// 返回处理
const handleBack = () => {
  console.log('默认返回')
  uni.showToast({
    title: '执行返回操作',
    icon: 'none'
  })
}

// 自定义返回处理
const handleCustomBack = () => {
  console.log('自定义返回逻辑')
  uni.showModal({
    title: '提示',
    content: '确定要离开当前页面吗？',
    success: (res) => {
      if (res.confirm) {
        uni.navigateBack()
      }
    }
  })
}

// 显示菜单
const showMenu = () => {
  uni.showToast({
    title: '显示菜单',
    icon: 'none'
  })
}

// 分享内容
const shareContent = () => {
  uni.showToast({
    title: '分享功能',
    icon: 'none'
  })
}

// 显示更多
const showMore = () => {
  uni.showActionSheet({
    itemList: ['收藏', '举报', '复制链接'],
    success: (res) => {
      console.log('选择了第' + (res.tapIndex + 1) + '个按钮')
    }
  })
}

// 页面加载时执行
onMounted(() => {
  // 获取固定导航栏高度
  setTimeout(() => {
    if (fixedNavbar.value) {
      fixedNavbarHeight.value = fixedNavbar.value.getNavbarHeight()
    }
  }, 100)
})
</script>

<style lang="scss" scoped>
.demo-page {
  background-color: #f5f5f5;
}

.demo-section {
  margin-bottom: 40rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.demo-content {
  padding: 40rpx 30rpx;
  
  .demo-title {
    display: block;
    font-size: 32rpx;
    font-weight: bold;
    color: #333333;
    margin-bottom: 20rpx;
  }
  
  .demo-desc {
    display: block;
    font-size: 28rpx;
    color: #666666;
    line-height: 1.5;
  }
}

.custom-left {
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  
  .menu-text {
    font-size: 36rpx;
    color: #ffffff;
  }
}

.custom-right {
  display: flex;
  align-items: center;
  gap: 30rpx;
  
  .share-btn,
  .more-btn {
    font-size: 28rpx;
    color: #ffffff;
    padding: 10rpx 20rpx;
  }
  
  .more-btn {
    font-size: 32rpx;
    font-weight: bold;
  }
}

.scroll-content {
  margin-top: 40rpx;
  
  .scroll-item {
    padding: 30rpx 20rpx;
    margin-bottom: 20rpx;
    background-color: #f8f8f8;
    border-radius: 12rpx;
    
    text {
      font-size: 28rpx;
      color: #333333;
    }
  }
}
</style>
