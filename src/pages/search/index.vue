<template>
  <view class="container">
    <!-- 搜索框 -->
    <view class="top">
      <view class="top_search">
        <image src="/images/default/map.png" />
        <input type="text" placeholder="请输入小区名称开始找房" bindinput="searchInput" />
      </view>
      <view class="top_search_btn" bindtap="searchBtn">搜索</view>
    </view>
    <!-- 历史搜索 -->
    <view class="lishi_box" wx:if="{{history.length>0}}">
      <view class="lishi_title">
        <text>搜索历史</text>
        <image class="delImg" src="/images/default/delimg.png" bindtap="clearHistory"></image>
      </view>
      <view class="lishi_content">
        <view class="lishi_item" wx:for="{{history}}" wx:key="{{history}}" data-name="{{item}}" catchtap="searchSome">
          {{ item }}
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'

// 页面数据
const searchKeyword = ref('')
const searchResults = ref([])
const loading = ref(false)

// 搜索输入处理
const onSearchInput = (e: any) => {
  searchKeyword.value = e.detail.value
}

// 执行搜索
const handleSearch = async () => {
  if (!searchKeyword.value.trim()) {
    uni.showToast({
      title: '请输入搜索关键词',
      icon: 'none',
    })
    return
  }

  try {
    loading.value = true
    // TODO: 调用搜索API
    console.log('搜索关键词:', searchKeyword.value)
  } catch (error) {
    console.error('搜索失败:', error)
  } finally {
    loading.value = false
  }
}

// 页面加载时执行
onMounted(() => {
  console.log('找房页面加载完成')
})
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  position: relative;
}

/*头部  */
.top {
  width: 100%;
  height: 66rpx;
  display: flex;
  align-items: center;
  border-radius: 33rpx;
  border: 1rpx solid #dbdbdb;
}

/*搜索框  */
.top_search {
  padding: 0 30rpx;
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}

.top_search image {
  width: 26rpx;
  height: 26rpx;
}

.top_search input {
  flex: 1;
  height: 80rpx;
  font-size: 26rpx;
  color: #4d4d4d;
  margin-left: 22rpx;
}

/*确定按钮  */
.top_search_btn {
  width: 94rpx;
  height: 26rpx;
  line-height: 26rpx;
  font-size: 26rpx;
  color: #8cc224;
  text-align: center;
  border-left: 1rpx solid #dbdbdb;
}

/*历史搜索  */
.lishi_box {
  margin-top: 38rpx;
}

.lishi_title {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.lishi_title text {
  font-size: 28rpx;
  color: #000000;
}

.lishi_content {
  display: flex;
  flex-wrap: wrap;
}

.lishi_item {
  padding: 0 30rpx;
  height: 66rpx;
  line-height: 66rpx;
  font-size: 26rpx;
  color: #4d4d4d;
  background: #f8f8f8;
  border-radius: 16rpx;
  margin-right: 20rpx;
  margin-top: 28rpx;
}

/* 删除 */
.delImg {
  width: 28rpx;
  height: 26rpx;
}
</style>
