<template>
  <view class="search-page">
    <view class="header">
      <text class="title">找房</text>
    </view>
    <view class="search-section">
      <view class="search-box">
        <input 
          v-model="searchKeyword" 
          placeholder="请输入关键词搜索房源"
          class="search-input"
          @input="onSearchInput"
        />
        <button class="search-btn" @click="handleSearch">搜索</button>
      </view>
    </view>
    <view class="content">
      <view class="search-results">
        <text class="placeholder" v-if="!searchResults.length">
          {{ searchKeyword ? '暂无搜索结果' : '请输入关键词开始搜索' }}
        </text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 页面数据
const searchKeyword = ref('')
const searchResults = ref([])
const loading = ref(false)

// 搜索输入处理
const onSearchInput = (e: any) => {
  searchKeyword.value = e.detail.value
}

// 执行搜索
const handleSearch = async () => {
  if (!searchKeyword.value.trim()) {
    uni.showToast({
      title: '请输入搜索关键词',
      icon: 'none'
    })
    return
  }
  
  try {
    loading.value = true
    // TODO: 调用搜索API
    console.log('搜索关键词:', searchKeyword.value)
  } catch (error) {
    console.error('搜索失败:', error)
  } finally {
    loading.value = false
  }
}

// 页面加载时执行
onMounted(() => {
  console.log('找房页面加载完成')
})
</script>

<style lang="scss" scoped>
.search-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  
  .header {
    padding: 20rpx;
    background-color: #fff;
    text-align: center;
    
    .title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }
  }
  
  .search-section {
    padding: 20rpx;
    background-color: #fff;
    
    .search-box {
      display: flex;
      align-items: center;
      gap: 20rpx;
      
      .search-input {
        flex: 1;
        height: 80rpx;
        padding: 0 20rpx;
        border: 2rpx solid #e0e0e0;
        border-radius: 40rpx;
        font-size: 28rpx;
      }
      
      .search-btn {
        height: 80rpx;
        padding: 0 30rpx;
        background-color: #007aff;
        color: #fff;
        border: none;
        border-radius: 40rpx;
        font-size: 28rpx;
      }
    }
  }
  
  .content {
    padding: 20rpx;
    
    .search-results {
      background-color: #fff;
      border-radius: 16rpx;
      padding: 40rpx 20rpx;
      text-align: center;
      
      .placeholder {
        font-size: 28rpx;
        color: #999;
      }
    }
  }
}
</style>
