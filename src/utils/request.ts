const gcid = '168'
const wechatId = '0b4b6302493b4c2d85ae3eca029c4292'
export const imgUrl = 'http://nas.yinbh.com:18888/files/mp/images'

// 定义响应数据接口
interface ResponseData<T = any> {
  code: number
  data: T
  message: string
}

// 请求配置接口
interface RequestOptions {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  data?: any
  params?: Record<string, any>
  header?: Record<string, any>
  timeout?: number
}

// 基础URL，从环境变量获取
//@ts-ignore
const BASE_URL = BASE_URL || ''

// 请求超时时间
const TIMEOUT = 15000

// 请求函数
function request<T = any>(options: RequestOptions): Promise<T> {
  return new Promise((resolve, reject) => {
    // 处理URL参数
    let url = options.url
    if (options.params) {
      const queryString = Object.keys(options.params)
        .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(options.params![key])}`)
        .join('&')
      url += (url.includes('?') ? '&' : '?') + queryString
    }

    // 获取token
    const token = uni.getStorageSync('token')

    // 发起请求
    uni.request({
      url: BASE_URL + url,
      data: options.data,
      method: options.method || 'GET',
      timeout: options.timeout || TIMEOUT,
      header: {
        'Content-Type': 'application/json',
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
        ...options.header,
      },
      success: (res) => {
        const data = res.data as ResponseData<T>

        // 根据状态码处理响应
        if (res.statusCode === 200) {
          // 业务状态码处理
          if (data.code === 200) {
            resolve(data.data)
          } else {
            // 显示错误信息
            uni.showToast({
              title: data.message || '请求失败',
              icon: 'none',
            })

            // 特定错误码处理，例如401未授权
            if (data.code === 401) {
              // 清除用户信息并跳转到登录页
              uni.removeStorageSync('token')
              uni.removeStorageSync('user')
              uni.navigateTo({
                url: '/pages/login/index',
              })
            }

            reject(data)
          }
        } else {
          uni.showToast({
            title: `网络错误(${res.statusCode})`,
            icon: 'none',
          })
          reject(res)
        }
      },
      fail: (err) => {
        uni.showToast({
          title: '网络异常，请检查网络连接',
          icon: 'none',
        })
        reject(err)
      },
    })
  })
}

// 封装GET请求
export function get<T = any>(url: string, params?: Record<string, any>, header?: Record<string, any>): Promise<T> {
  return request<T>({
    url,
    method: 'GET',
    params,
    header,
  })
}

// 封装POST请求
export function post<T = any>(url: string, data?: any, header?: Record<string, any>): Promise<T> {
  return request<T>({
    url,
    method: 'POST',
    data,
    header,
  })
}
