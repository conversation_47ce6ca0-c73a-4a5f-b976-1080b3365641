# RoomCard 房间卡片组件

一个用于展示房间信息的卡片组件，包含房间图片、标题、标签、详情、位置和价格等信息。

## 功能特性

- ✅ 房间图片展示
- ✅ 房间标题和标签
- ✅ 房间详情（面积、楼层、朝向）
- ✅ 位置信息展示
- ✅ 价格信息展示
- ✅ 点击事件支持
- ✅ 响应式设计
- ✅ 卡片阴影效果

## Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| roomData | RoomData | - | 房间数据对象 |

### RoomData 类型定义

```typescript
interface RoomData {
  id: number        // 房间ID
  title: string     // 房间标题
  size: string      // 房间面积
  type: string      // 房间类型（楼层、朝向等）
  address: string   // 房间地址
  price: string     // 房间价格
  image: string     // 房间图片URL
  tags: string[]    // 房间标签数组
}
```

## Events 事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| click | 卡片点击事件 | roomId: number |

## 使用示例

### 基础用法

```vue
<template>
  <view>
    <RoomCard 
      :room-data="roomData"
      @click="handleRoomClick"
    />
  </view>
</template>

<script setup>
import { RoomCard } from '@/components'

const roomData = {
  id: 1,
  title: '整租·精品一居·菊园社区',
  size: '28.9㎡',
  type: '9/11层 | 朝南',
  address: '上海市嘉定区祁连路1255号',
  price: '1550',
  image: '/static/images/room1.jpg',
  tags: ['保租房']
}

const handleRoomClick = (roomId) => {
  console.log('点击房间:', roomId)
  // 跳转到房间详情页
  uni.navigateTo({
    url: `/pages/room-detail/index?id=${roomId}`
  })
}
</script>
```

### 列表用法

```vue
<template>
  <view class="room-list">
    <RoomCard 
      v-for="room in roomList" 
      :key="room.id"
      :room-data="room"
      @click="goToRoomDetail"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { RoomCard } from '@/components'

const roomList = ref([
  {
    id: 1,
    title: '整租·精品一居·菊园社区',
    size: '28.9㎡',
    type: '9/11层 | 朝南',
    address: '上海市嘉定区祁连路1255号',
    price: '1550',
    image: '/static/images/room1.jpg',
    tags: ['保租房']
  },
  {
    id: 2,
    title: '整租·精品一居·菊园社区',
    size: '28.9㎡',
    type: '9/11层 | 朝南',
    address: '上海市嘉定区祁连路1255号',
    price: '1550',
    image: '/static/images/room2.jpg',
    tags: ['保租房']
  }
])

const goToRoomDetail = (roomId) => {
  uni.navigateTo({
    url: `/pages/room-detail/index?id=${roomId}`
  })
}
</script>
```

## 样式说明

组件使用了以下主要样式特性：

- **卡片容器**: 白色背景，16rpx圆角，阴影效果
- **图片**: 300rpx高度，aspectFill模式
- **标题**: 30rpx字体，粗体，深色
- **标签**: 蓝色背景，圆角设计
- **价格**: 36rpx字体，绿色高亮
- **间距**: 统一的内边距和外边距

## 注意事项

1. 确保传入的 `roomData` 包含所有必需的字段
2. 图片路径需要是有效的资源路径
3. 价格字段为字符串类型，组件内部会自动添加"元/月起"后缀
4. 标签数组为空时不会显示标签区域
5. 组件会自动处理最后一个卡片的底部间距

## 扩展建议

如需扩展功能，可以考虑：

1. 添加收藏功能
2. 添加更多房间状态标识
3. 支持自定义卡片样式
4. 添加加载状态
5. 支持骨架屏效果
