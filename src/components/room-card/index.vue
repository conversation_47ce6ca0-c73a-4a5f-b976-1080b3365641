<template>
  <view class="room-card" @click="handleClick">
    <image :src="roomData.image" class="room-image" mode="aspectFill" />
    
    <view class="room-info">
      <view class="room-header">
        <text class="room-title">{{ roomData.title }}</text>
        <view class="room-tags">
          <text 
            v-for="(tag, tagIndex) in roomData.tags" 
            :key="tagIndex"
            class="room-tag"
          >
            {{ tag }}
          </text>
        </view>
      </view>
      
      <view class="room-details">
        <text class="room-size">{{ roomData.size }}</text>
        <text class="room-type">{{ roomData.type }}</text>
      </view>
      
      <view class="room-location">
        <text class="location-icon">📍</text>
        <text class="room-address">{{ roomData.address }}</text>
      </view>
      
      <view class="room-price">
        <text class="price">{{ roomData.price }}</text>
        <text class="price-unit">元/月起</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
// Props 定义
interface RoomData {
  id: number
  title: string
  size: string
  type: string
  address: string
  price: string
  image: string
  tags: string[]
}

interface Props {
  roomData: RoomData
}

const props = defineProps<Props>()

// Emits 定义
const emit = defineEmits<{
  click: [roomId: number]
}>()

// 点击处理
const handleClick = () => {
  emit('click', props.roomData.id)
}
</script>

<style lang="scss" scoped>
.room-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

  &:last-child {
    margin-bottom: 0;
  }

  .room-image {
    width: 100%;
    height: 300rpx;
  }

  .room-info {
    padding: 30rpx;

    .room-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 15rpx;

      .room-title {
        font-size: 30rpx;
        font-weight: bold;
        color: #333333;
        flex: 1;
      }

      .room-tags {
        display: flex;
        gap: 10rpx;

        .room-tag {
          background-color: #e3f2fd;
          color: #1976d2;
          font-size: 20rpx;
          padding: 8rpx 16rpx;
          border-radius: 20rpx;
        }
      }
    }

    .room-details {
      display: flex;
      gap: 20rpx;
      margin-bottom: 15rpx;

      .room-size,
      .room-type {
        font-size: 24rpx;
        color: #666666;
      }
    }

    .room-location {
      display: flex;
      align-items: center;
      gap: 10rpx;
      margin-bottom: 15rpx;

      .location-icon {
        font-size: 24rpx;
        color: #666666;
      }

      .room-address {
        font-size: 24rpx;
        color: #666666;
      }
    }

    .room-price {
      display: flex;
      align-items: baseline;
      gap: 5rpx;

      .price {
        font-size: 36rpx;
        font-weight: bold;
        color: #4caf50;
      }

      .price-unit {
        font-size: 24rpx;
        color: #999999;
      }
    }
  }
}
</style>
