<template>
  <view class="room-card" @click="handleClick">
    <image :src="roomData.image" class="room-image" mode="aspectFill" />

    <view class="room-info">
      <view class="room-title">{{ roomData.title }}</view>

      <view class="room-dec">
        {{ roomData.area }}㎡<view class="line"></view>{{ roomData.floor }}/{{ roomData.totalFloor
        }}<view class="line"></view>朝{{ roomData.orientationName }}
      </view>

      <view class="room-location">
        <image class="location-icon" src="@/static/icon/address-icon.png"></image>
        <text class="room-address">{{ roomData.address }}</text>
      </view>

      <view class="room-tag">
        {{ roomData.tag }}
      </view>

      <view class="room-price">
        <text class="price">{{ roomData.price }}</text>
        <text class="price-unit">元/月起</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
// Props 定义
interface RoomData {
  id: number
  title: string
  area: string
  floor: number
  totalFloor: number
  orientationName: string
  address: string
  price: string
  image: string
  tag: string
}

interface Props {
  roomData: RoomData
}

const props = defineProps<Props>()

// Emits 定义
const emit = defineEmits<{
  click: [roomId: number]
}>()

// 点击处理
const handleClick = () => {
  emit('click', props.roomData.id)
}
</script>

<style lang="scss" scoped>
.room-card {
  display: flex;
  align-items: center;

  .room-image {
    width: 300rpx;
    height: 200rpx;
    border-radius: 16rpx;
  }

  .room-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-left: 10rpx;
    height: 200rpx;

    .room-title {
      font-size: 28rpx;
      font-weight: bold;
      color: #000000;
      width: 100%;
      @include text-overflow(1);
    }

    .room-dec {
      font-size: 22rpx;
      color: #808080;
      display: flex;
      align-items: center;
      .line {
        width: 1rpx;
        height: 24rpx;
        margin: 0 12rpx;
        background: #808080;
      }
    }

    .room-location {
      display: flex;
      align-items: center;
      gap: 10rpx;

      .location-icon {
        width: 22rpx;
        height: 22rpx;
      }

      .room-address {
        font-size: 22rpx;
        color: #808080;
      }
    }

    .room-tag {
      align-self: flex-start;
      background-color: #00a8cf;
      color: #ffffff;
      font-size: 22rpx;
      padding: 8rpx;
      border-radius: 4rpx;
    }

    .room-price {
      display: flex;
      align-items: baseline;
      gap: 5rpx;
      color: #8cc224;

      .price {
        font-size: 32rpx;
        font-weight: bold;
      }

      .price-unit {
        font-size: 24rpx;
      }
    }
  }
}
</style>
