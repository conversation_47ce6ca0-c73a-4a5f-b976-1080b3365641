# CustomNavbar 自定义导航栏组件

一个功能完整的自定义导航栏组件，支持标题、返回按钮、背景颜色自定义等功能。

## 功能特性

- ✅ 自定义标题文字
- ✅ 可选择显示/隐藏返回按钮
- ✅ 自定义返回图标
- ✅ 自定义背景颜色和标题颜色
- ✅ 支持固定定位（fixed）
- ✅ 自动适配状态栏高度
- ✅ 支持插槽自定义左中右区域
- ✅ 可自定义样式和层级

## Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| title | String | '' | 导航栏标题 |
| showBack | Boolean | true | 是否显示返回按钮 |
| backIcon | String | '' | 自定义返回图标路径 |
| backgroundColor | String | '#ffffff' | 背景颜色 |
| titleColor | String | '#333333' | 标题文字颜色 |
| fixed | Boolean | false | 是否固定定位 |
| zIndex | Number | 999 | z-index 层级 |
| borderBottom | Boolean | true | 是否显示底部边框 |
| customStyle | Object | {} | 自定义样式对象 |

## Events 事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| back | 返回按钮点击事件 | - |

## Slots 插槽

| 插槽名 | 说明 |
|--------|------|
| left | 左侧区域自定义内容 |
| center | 中间区域自定义内容 |
| right | 右侧区域自定义内容 |

## 暴露方法

| 方法名 | 说明 | 返回值 |
|--------|------|--------|
| getNavbarHeight | 获取导航栏总高度 | Number |
| getStatusBarHeight | 获取状态栏高度 | Number |
| getNavBarHeight | 获取导航栏内容高度 | Number |

## 使用示例

### 基础用法

```vue
<template>
  <view>
    <CustomNavbar title="页面标题" />
    <view class="content">
      <!-- 页面内容 -->
    </view>
  </view>
</template>

<script setup>
import CustomNavbar from '@/components/navbar/index.vue'
</script>
```

### 固定定位用法

```vue
<template>
  <view>
    <CustomNavbar 
      title="固定导航栏" 
      :fixed="true"
      background-color="#007aff"
      title-color="#ffffff"
    />
    <!-- 需要添加占位高度 -->
    <view :style="{ paddingTop: navbarHeight + 'px' }">
      <view class="content">
        <!-- 页面内容 -->
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import CustomNavbar from '@/components/navbar/index.vue'

const navbarRef = ref()
const navbarHeight = ref(0)

onMounted(() => {
  // 获取导航栏高度用于占位
  navbarHeight.value = navbarRef.value?.getNavbarHeight() || 88
})
</script>
```

### 自定义返回图标

```vue
<template>
  <CustomNavbar 
    title="自定义返回图标"
    back-icon="/images/icon/custom-back.png"
    @back="handleBack"
  />
</template>

<script setup>
const handleBack = () => {
  console.log('自定义返回逻辑')
  // 执行自定义返回操作
}
</script>
```

### 使用插槽自定义

```vue
<template>
  <CustomNavbar title="自定义导航栏">
    <template #left>
      <view class="custom-left">
        <image src="/images/icon/menu.png" class="menu-icon" />
      </view>
    </template>
    
    <template #right>
      <view class="custom-right">
        <text class="share-btn">分享</text>
      </view>
    </template>
  </CustomNavbar>
</template>
```

### 完全自定义样式

```vue
<template>
  <CustomNavbar 
    title="自定义样式"
    background-color="linear-gradient(45deg, #ff6b6b, #4ecdc4)"
    title-color="#ffffff"
    :border-bottom="false"
    :custom-style="{
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
      borderRadius: '0 0 20rpx 20rpx'
    }"
  />
</template>
```

## 注意事项

1. 使用固定定位时，记得为页面内容添加对应的 `padding-top` 或 `margin-top`
2. 在小程序中，状态栏高度会自动适配不同机型
3. 如果需要自定义返回逻辑，请监听 `@back` 事件
4. 组件会自动处理安全区域适配
