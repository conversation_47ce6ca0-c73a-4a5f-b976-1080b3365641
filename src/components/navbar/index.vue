<template>
  <view class="custom-navbar" :class="{ 'navbar-fixed': fixed }" :style="navbarStyle">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

    <!-- 导航栏内容 -->
    <view class="navbar-content" :style="{ height: navBarHeight + 'px' }">
      <!-- 左侧区域 -->
      <view class="navbar-left">
        <view v-if="showBack" class="back-btn" @click="handleBack">
          <image :src="backIcon" class="back-icon" />
        </view>
        <slot name="left"></slot>
      </view>

      <!-- 中间标题区域 -->
      <view class="navbar-center">
        <text v-if="title" class="navbar-title" :style="titleStyle">{{ title }}</text>
        <slot name="center"></slot>
      </view>

      <!-- 右侧区域 -->
      <view class="navbar-right">
        <slot name="right"></slot>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'

import backIconDefault from '@/static/icon/back.png'

// Props 定义
interface Props {
  title?: string // 标题文字
  showBack?: boolean // 是否显示返回按钮
  backIcon?: string // 自定义返回图标
  backgroundColor?: string // 背景颜色
  titleColor?: string // 标题颜色
  fixed?: boolean // 是否固定定位
  zIndex?: number // z-index 层级
  borderBottom?: boolean // 是否显示底部边框
  customStyle?: Record<string, any> // 自定义样式
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  showBack: true,
  backIcon: backIconDefault,
  backgroundColor: '#ffffff',
  titleColor: '#333333',
  fixed: false,
  zIndex: 999,
  borderBottom: true,
  customStyle: () => ({}),
})

// Emits 定义
const emit = defineEmits<{
  back: []
}>()

// 响应式数据
const statusBarHeight = ref(0)
const navBarHeight = ref(44)

// 计算属性
const navbarStyle = computed(() => {
  return {
    backgroundColor: props.backgroundColor,
    zIndex: props.zIndex,
    borderBottom: props.borderBottom ? '1rpx solid #e5e5e5' : 'none',
    ...props.customStyle,
  }
})

const titleStyle = computed(() => {
  return {
    color: props.titleColor,
  }
})

// 获取系统信息
const getSystemInfo = () => {
  try {
    const systemInfo = uni.getSystemInfoSync()
    statusBarHeight.value = systemInfo.statusBarHeight || 0

    // 根据平台设置导航栏高度
    // #ifdef MP-WEIXIN
    navBarHeight.value = 44
    // #endif

    // #ifdef H5
    navBarHeight.value = 44
    // #endif

    // #ifdef APP-PLUS
    navBarHeight.value = 44
    // #endif
  } catch (error) {
    console.error('获取系统信息失败:', error)
    statusBarHeight.value = 20
    navBarHeight.value = 44
  }
}

// 返回按钮点击处理
const handleBack = () => {
  emit('back')

  // 如果没有监听 back 事件，则执行默认返回操作
  const pages = getCurrentPages()
  if (pages.length > 1) {
    uni.navigateBack({
      delta: 1,
    })
  } else {
    // 如果是首页，可以跳转到首页或者提示
    uni.switchTab({
      url: '/pages/index/index',
    })
  }
}

// 页面加载时执行
onMounted(() => {
  getSystemInfo()
})

// 暴露方法给父组件
defineExpose({
  getNavbarHeight: () => statusBarHeight.value + navBarHeight.value,
  getStatusBarHeight: () => statusBarHeight.value,
  getNavBarHeight: () => navBarHeight.value,
})
</script>

<style lang="scss" scoped>
.custom-navbar {
  width: 100%;
  background-color: #ffffff;

  &.navbar-fixed {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
  }

  .status-bar {
    width: 100%;
  }

  .navbar-content {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20rpx;
    box-sizing: border-box;

    .navbar-left {
      display: flex;
      align-items: center;
      min-width: 120rpx;

      .back-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60rpx;
        height: 60rpx;
        margin-right: 20rpx;

        .back-icon {
          width: 40rpx;
          height: 40rpx;
        }

        .back-text {
          font-size: 40rpx;
          color: #333333;
          font-weight: bold;
        }
      }
    }

    .navbar-center {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      .navbar-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333333;
        text-align: center;
        max-width: 400rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .navbar-right {
      display: flex;
      align-items: center;
      min-width: 120rpx;
      justify-content: flex-end;
    }
  }
}
</style>
