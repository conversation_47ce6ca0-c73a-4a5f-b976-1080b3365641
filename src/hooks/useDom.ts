/**
 * DOM 操作相关的 Composition API Hook
 */

import { ref, onMounted, getCurrentInstance, nextTick } from 'vue'
import { 
  getElementHeightByClass, 
  getElementHeightById, 
  getElementRect,
  getMultipleElementsHeight,
  getElementHeightWhenReady,
  getSystemSafeArea
} from '@/utils/dom'

/**
 * 使用元素高度的 Hook
 * @param className 类名
 * @param immediate 是否立即查询，默认为 true
 * @returns { height, getHeight, loading }
 */
export const useElementHeight = (className: string, immediate: boolean = true) => {
  const height = ref(0)
  const loading = ref(false)
  const instance = getCurrentInstance()

  const getHeight = async () => {
    try {
      loading.value = true
      const result = await getElementHeightByClass(className, instance)
      height.value = result
      return result
    } catch (error) {
      console.error('获取元素高度失败:', error)
      return 0
    } finally {
      loading.value = false
    }
  }

  if (immediate) {
    onMounted(async () => {
      await nextTick()
      await getHeight()
    })
  }

  return {
    height,
    getHeight,
    loading
  }
}

/**
 * 使用元素完整尺寸信息的 Hook
 * @param selector 选择器
 * @param immediate 是否立即查询，默认为 true
 * @returns { rect, getRect, loading }
 */
export const useElementRect = (selector: string, immediate: boolean = true) => {
  const rect = ref({
    width: 0,
    height: 0,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0
  })
  const loading = ref(false)
  const instance = getCurrentInstance()

  const getRect = async () => {
    try {
      loading.value = true
      const result = await getElementRect(selector, instance)
      rect.value = result
      return result
    } catch (error) {
      console.error('获取元素尺寸失败:', error)
      return rect.value
    } finally {
      loading.value = false
    }
  }

  if (immediate) {
    onMounted(async () => {
      await nextTick()
      await getRect()
    })
  }

  return {
    rect,
    getRect,
    loading
  }
}

/**
 * 使用多个元素高度的 Hook
 * @param classNames 类名数组
 * @param immediate 是否立即查询，默认为 true
 * @returns { heights, getHeights, loading }
 */
export const useMultipleElementsHeight = (classNames: string[], immediate: boolean = true) => {
  const heights = ref<number[]>([])
  const loading = ref(false)
  const instance = getCurrentInstance()

  const getHeights = async () => {
    try {
      loading.value = true
      const results = await getMultipleElementsHeight(classNames, instance)
      heights.value = results
      return results
    } catch (error) {
      console.error('获取多个元素高度失败:', error)
      return []
    } finally {
      loading.value = false
    }
  }

  if (immediate) {
    onMounted(async () => {
      await nextTick()
      await getHeights()
    })
  }

  return {
    heights,
    getHeights,
    loading
  }
}

/**
 * 使用动态元素高度的 Hook（等待元素渲染完成）
 * @param className 类名
 * @param maxRetries 最大重试次数
 * @param retryDelay 重试间隔
 * @returns { height, getHeight, loading }
 */
export const useDynamicElementHeight = (
  className: string, 
  maxRetries: number = 10, 
  retryDelay: number = 100
) => {
  const height = ref(0)
  const loading = ref(false)
  const instance = getCurrentInstance()

  const getHeight = async () => {
    try {
      loading.value = true
      const result = await getElementHeightWhenReady(className, instance, maxRetries, retryDelay)
      height.value = result
      return result
    } catch (error) {
      console.error('获取动态元素高度失败:', error)
      return 0
    } finally {
      loading.value = false
    }
  }

  onMounted(async () => {
    await nextTick()
    await getHeight()
  })

  return {
    height,
    getHeight,
    loading
  }
}

/**
 * 使用系统安全区域的 Hook
 * @returns { safeArea, getSafeArea }
 */
export const useSystemSafeArea = () => {
  const safeArea = ref({
    statusBarHeight: 0,
    safeAreaTop: 0,
    safeAreaBottom: 0
  })

  const getSafeArea = async () => {
    try {
      const result = await getSystemSafeArea()
      safeArea.value = result
      return result
    } catch (error) {
      console.error('获取系统安全区域失败:', error)
      return safeArea.value
    }
  }

  onMounted(() => {
    getSafeArea()
  })

  return {
    safeArea,
    getSafeArea
  }
}

/**
 * 使用页面布局计算的 Hook
 * @param headerClass 头部类名
 * @param footerClass 底部类名（可选）
 * @returns { contentHeight, headerHeight, footerHeight, calculate }
 */
export const usePageLayout = (headerClass: string, footerClass?: string) => {
  const contentHeight = ref(0)
  const headerHeight = ref(0)
  const footerHeight = ref(0)
  const loading = ref(false)
  const instance = getCurrentInstance()

  const calculate = async () => {
    try {
      loading.value = true
      
      // 获取系统信息
      const systemInfo = uni.getSystemInfoSync()
      
      // 获取头部高度
      const hHeight = await getElementHeightByClass(headerClass, instance)
      headerHeight.value = hHeight
      
      // 获取底部高度（如果存在）
      let fHeight = 0
      if (footerClass) {
        fHeight = await getElementHeightByClass(footerClass, instance)
        footerHeight.value = fHeight
      }
      
      // 计算内容区域高度
      contentHeight.value = systemInfo.windowHeight - hHeight - fHeight
      
      return {
        contentHeight: contentHeight.value,
        headerHeight: headerHeight.value,
        footerHeight: footerHeight.value
      }
    } catch (error) {
      console.error('计算页面布局失败:', error)
      return {
        contentHeight: 0,
        headerHeight: 0,
        footerHeight: 0
      }
    } finally {
      loading.value = false
    }
  }

  onMounted(async () => {
    await nextTick()
    await calculate()
  })

  return {
    contentHeight,
    headerHeight,
    footerHeight,
    loading,
    calculate
  }
}
