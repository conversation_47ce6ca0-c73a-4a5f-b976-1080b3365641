<template>
  <view class="example-page">
    <!-- 示例1: 基础用法 -->
    <view class="example-section">
      <view class="example-header">示例1: 基础高度查询</view>
      <view class="test-element">测试元素</view>
      <view class="result">
        <text>元素高度: {{ basicHeight }}px</text>
        <button @click="getBasicHeight" :disabled="basicLoading">
          {{ basicLoading ? '查询中...' : '获取高度' }}
        </button>
      </view>
    </view>

    <!-- 示例2: 使用 Hook -->
    <view class="example-section">
      <view class="example-header">示例2: 使用 Hook 自动获取</view>
      <view class="hook-test-element">Hook测试元素</view>
      <view class="result">
        <text>Hook高度: {{ hookHeight }}px</text>
        <text v-if="hookLoading">加载中...</text>
      </view>
    </view>

    <!-- 示例3: 页面布局计算 -->
    <view class="example-section">
      <view class="layout-header">页面头部</view>
      <view class="layout-content" :style="{ height: layoutContentHeight + 'px' }">
        <text>内容区域高度: {{ layoutContentHeight }}px</text>
        <text>头部高度: {{ layoutHeaderHeight }}px</text>
        <text>底部高度: {{ layoutFooterHeight }}px</text>
      </view>
      <view class="layout-footer">页面底部</view>
    </view>

    <!-- 示例4: 多个元素高度 -->
    <view class="example-section">
      <view class="example-header">示例4: 批量获取高度</view>
      <view class="multi-element-1">元素1</view>
      <view class="multi-element-2">元素2</view>
      <view class="multi-element-3">元素3</view>
      <view class="result">
        <text>多个元素高度: {{ multiHeights.join(', ') }}px</text>
        <button @click="getMultiHeights" :disabled="multiLoading">
          {{ multiLoading ? '查询中...' : '批量获取' }}
        </button>
      </view>
    </view>

    <!-- 示例5: 完整尺寸信息 -->
    <view class="example-section">
      <view class="example-header">示例5: 完整尺寸信息</view>
      <view class="rect-test-element">尺寸测试元素</view>
      <view class="result">
        <text>宽度: {{ rectInfo.width }}px</text>
        <text>高度: {{ rectInfo.height }}px</text>
        <text>位置: ({{ rectInfo.left }}, {{ rectInfo.top }})</text>
      </view>
    </view>

    <!-- 示例6: 系统安全区域 -->
    <view class="example-section">
      <view class="example-header">示例6: 系统安全区域</view>
      <view class="result">
        <text>状态栏高度: {{ safeAreaInfo.statusBarHeight }}px</text>
        <text>安全区域顶部: {{ safeAreaInfo.safeAreaTop }}px</text>
        <text>安全区域底部: {{ safeAreaInfo.safeAreaBottom }}px</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getElementHeightByClass, getMultipleElementsHeight } from '@/utils/dom'
import { 
  useElementHeight, 
  usePageLayout, 
  useElementRect,
  useSystemSafeArea 
} from '@/hooks/useDom'

// 示例1: 基础用法
const basicHeight = ref(0)
const basicLoading = ref(false)

const getBasicHeight = async () => {
  basicLoading.value = true
  try {
    const height = await getElementHeightByClass('test-element')
    basicHeight.value = height
  } catch (error) {
    console.error('获取基础高度失败:', error)
  } finally {
    basicLoading.value = false
  }
}

// 示例2: 使用 Hook
const { height: hookHeight, loading: hookLoading } = useElementHeight('hook-test-element')

// 示例3: 页面布局计算
const { 
  contentHeight: layoutContentHeight, 
  headerHeight: layoutHeaderHeight, 
  footerHeight: layoutFooterHeight 
} = usePageLayout('layout-header', 'layout-footer')

// 示例4: 多个元素高度
const multiHeights = ref<number[]>([])
const multiLoading = ref(false)

const getMultiHeights = async () => {
  multiLoading.value = true
  try {
    const heights = await getMultipleElementsHeight([
      'multi-element-1',
      'multi-element-2', 
      'multi-element-3'
    ])
    multiHeights.value = heights
  } catch (error) {
    console.error('获取多个高度失败:', error)
  } finally {
    multiLoading.value = false
  }
}

// 示例5: 完整尺寸信息
const { rect: rectInfo } = useElementRect('.rect-test-element')

// 示例6: 系统安全区域
const { safeArea: safeAreaInfo } = useSystemSafeArea()
</script>

<style lang="scss" scoped>
.example-page {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.example-section {
  margin-bottom: 40rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.example-header {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  border-bottom: 2rpx solid #e0e0e0;
  padding-bottom: 10rpx;
}

.test-element,
.hook-test-element,
.multi-element-1,
.multi-element-2,
.multi-element-3,
.rect-test-element {
  height: 100rpx;
  background-color: #4caf50;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 28rpx;
  margin-bottom: 20rpx;
}

.multi-element-1 {
  background-color: #2196f3;
}

.multi-element-2 {
  background-color: #ff9800;
}

.multi-element-3 {
  background-color: #e91e63;
}

.rect-test-element {
  width: 300rpx;
  height: 150rpx;
  background-color: #9c27b0;
}

.result {
  display: flex;
  flex-direction: column;
  gap: 10rpx;

  text {
    font-size: 26rpx;
    color: #666666;
    padding: 5rpx 0;
  }

  button {
    margin-top: 10rpx;
    padding: 15rpx 30rpx;
    background-color: #4caf50;
    color: #ffffff;
    border: none;
    border-radius: 8rpx;
    font-size: 26rpx;

    &:disabled {
      background-color: #cccccc;
    }
  }
}

// 页面布局示例样式
.layout-header {
  height: 100rpx;
  background-color: #2196f3;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
}

.layout-content {
  background-color: #f0f0f0;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  overflow-y: auto;

  text {
    font-size: 24rpx;
    color: #666666;
  }
}

.layout-footer {
  height: 80rpx;
  background-color: #ff5722;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
}
</style>
